import React, { useEffect, useState } from "react";
import * as _ from "lodash";
import { DataNode } from "antd/lib/tree";
import { useDispatch, useRequest, useSelector } from "src/hook";
import { getClassBuiltTempAPI } from "src/api/dataClassification";
import { setSelectedTemplateNodeInfo } from "../../../dataClassificationSlice";
import { setLoadedKeys } from "src/pageTabs/queryPage/sdt";
import { formatTreeData } from '../util';
import {
  buildTreeFromResponseData,
  buildTreeFromCachedData,
  getAllDescendants,
  rebuildExpandedNodesData,
  findNodeInTreeWithChildren,
  findNodeByKey
} from '../utils/treeHelpers';

export interface UseTreeStateManagerResult {
  // 状态
  treeData: DataNode[];
  expandedKeys: any[];
  selectedKeys: any[];
  treeNodeChildrenMap: any;
  descendantsCache: Map<string, any[]>;
  rootLoading: boolean;
  
  // 操作函数
  setExpandedKeys: (keys: any[]) => void;
  setSelectedKeys: (keys: any[]) => void;
  onLoadData: (node: any) => Promise<void>;
  refreshTree: () => void;
  clearTpDataCache: (tp_id?: number) => void; // 新增：清空缓存函数
  
  // 组件用到的函数
  handleNodeSelect: (key: any, node: any) => Promise<void>;
}

export const useTreeStateManager = (needRefreshTree?: boolean): UseTreeStateManagerResult => {
  const dispatch = useDispatch();
  const { selectedTemplateNodeInfo } = useSelector(state => state.dataClassification);
  
  // 状态定义
  const [treeData, setTreeData] = useState<DataNode[]>([]);
  const [expandedKeys, setExpandedKeys] = useState<any[]>([]);
  const [selectedKeys, setSelectedKeys] = useState<any[]>([]);
  const [treeNodeChildrenMap, setTreeNodeChildrenMap] = useState<any>({});
  const [descendantsCache, setDescendantsCache] = useState<Map<string, any[]>>(new Map());
  // 新增：tp_id 全量数据缓存，避免重复API调用
  const [tpDataCache, setTpDataCache] = useState<Map<number, any[]>>(new Map());
  
  // 使用 ref 存储缓存引用，避免 useEffect 依赖循环
  const tpDataCacheRef = React.useRef(tpDataCache);
  React.useEffect(() => {
    tpDataCacheRef.current = tpDataCache;
  }, [tpDataCache]);

  // 添加ref来跟踪初始化状态
  const isInitializedRef = React.useRef(false);
  const prevNeedRefreshTreeRef = React.useRef(needRefreshTree);

  // 数据请求
  const { loading: rootLoading, run, refresh: refreshTree } = useRequest(() => {
    return getClassBuiltTempAPI({
      action: 'query_list',
      tp_id: '',
      limit: 10000
    });
  }, {
    manual: true,
    formatResult: (res: any) => {
      if (!res?.datas) return [];
      return formatTreeData(res.datas, true);
    },
    onSuccess: (res: any) => {
      setTreeData(res);
      setSelectedKeys([res?.[0]?.key ?? []]);
      const cloneNode = _.cloneDeep(res?.[0] ?? {});
      dispatch(setSelectedTemplateNodeInfo(cloneNode as any));
    }
  });

  // 初始加载数据
  useEffect(() => {
    run();
  }, [run]);

  // 恢复选中节点信息的辅助函数
  const restoreSelectedNodeInfo = React.useCallback(async (selectedKey: any, treeData: any[]) => {
    const updatedNode = findNodeInTreeWithChildren(treeData || [], selectedKey, treeNodeChildrenMap);

    if (updatedNode) {
      const updateNodeWithLatestData = async () => {
        let refreshedNode = _.cloneDeep(updatedNode);
        let allDescendants: any[] = [];
        
        try {
          // 对于分类节点，如果当前选中节点不在展开的节点列表中，需要重新获取最新数据
          if (updatedNode.nodeType === 'classify' && updatedNode.id && updatedNode.tp_id) {
            // 检查当前节点是否在已重建的展开节点中
            const isNodeRebuilt = expandedKeys.includes(selectedKey);
            
            if (!isNodeRebuilt) {
              // 当前选中节点没有被重建，需要重新获取最新数据
              const latestNodeData = await buildTreeFromResponseData(updatedNode.tp_id, updatedNode.parent_id, tpDataCacheRef.current);
              const currentNodeData = latestNodeData.find((node: any) => node.id === updatedNode.id);
              
              if (currentNodeData) {
                refreshedNode = {
                  ...currentNodeData,
                  key: updatedNode.key,
                  allDescendants: [] // 待下面填充
                };
              }
            }
            
            // 获取子孙数据
            allDescendants = await getAllDescendants(updatedNode.id, updatedNode.tp_id, descendantsCache);
          } else if (updatedNode.isRoot && updatedNode.tp_id) {
            // 对于根节点，直接获取子孙数据
            allDescendants = await getAllDescendants(0, updatedNode.tp_id, descendantsCache);
          }
        } catch (error) {
          allDescendants = [];
        }

        refreshedNode.allDescendants = allDescendants;
        dispatch(setSelectedTemplateNodeInfo(refreshedNode as any));
      };

      await updateNodeWithLatestData();
    }
  }, [dispatch, treeNodeChildrenMap, descendantsCache, expandedKeys]);

  // 监听needRefreshTree变化，进行智能刷新
  useEffect(() => {
    console.log('needRefreshTree useEffect triggered, value:', needRefreshTree, 'initialized:', isInitializedRef.current);

    // 如果是初始化阶段，只记录初始值，不执行刷新
    if (!isInitializedRef.current) {
      isInitializedRef.current = true;
      prevNeedRefreshTreeRef.current = false;
      console.log('初始化阶段，记录初始值:', needRefreshTree);
      return;
    }

    console.log('needRefreshTree值:', needRefreshTree, 'prevNeedRefreshTreeRef.current:', prevNeedRefreshTreeRef.current);
    // 只有当needRefreshTree值真正发生变化时才刷新
    if (needRefreshTree !== prevNeedRefreshTreeRef.current) {
      console.log('needRefreshTree值发生变化，开始刷新:', 'old:', prevNeedRefreshTreeRef.current, 'new:', needRefreshTree);
      prevNeedRefreshTreeRef.current = needRefreshTree;

      // 保存当前的展开状态和选中状态
      const currentExpandedKeys = expandedKeys;
      const currentSelectedKeys = selectedKeys;
      const currentSelectedNodeInfo = selectedTemplateNodeInfo;
      console.log('保存当前状态 - 展开:', currentExpandedKeys, '选中:', currentSelectedKeys);

      setExpandedKeys([]);
      setLoadedKeys([]);

      // 重新请求数据
      run().then((newTreeData) => {
        // 请求完成后按层级顺序恢复展开状态和选中状态
        setTimeout(async () => {

          // 清空当前状态
          setExpandedKeys([]);
          setLoadedKeys([]);
          setSelectedKeys([]);

          // 先重新构建展开节点的树结构
          if (currentExpandedKeys.length > 0) {
            const { newTreeData: rebuiltTreeData, newTreeNodeChildrenMap } = await rebuildExpandedNodesData(
              currentExpandedKeys, 
              newTreeData, 
              treeNodeChildrenMap,
              tpDataCacheRef.current
            );
            
            // 更新状态
            setTreeData(rebuiltTreeData);
            setTreeNodeChildrenMap(newTreeNodeChildrenMap);
            
            // 重新构建完成后，设置展开状态
            setExpandedKeys(currentExpandedKeys);
          }

          // 恢复选中状态
          if (currentSelectedKeys.length > 0) {
            setSelectedKeys(currentSelectedKeys);

            // 重新设置选中的节点信息，触发右侧数据更新
            if (currentSelectedNodeInfo) {
              await restoreSelectedNodeInfo(currentSelectedKeys[0], newTreeData);
            }
          }

        }, 1000);
      });
    }
  }, [needRefreshTree, expandedKeys, selectedKeys, run, restoreSelectedNodeInfo, selectedTemplateNodeInfo, treeNodeChildrenMap]);

  // 节点加载逻辑 - 优化版本使用缓存避免重复API调用
  const onLoadData = React.useCallback(async (node: any) => {
    const { nodeType } = node.props || node;
    
    // 如果是模板节点(isRoot为true)，直接加载其子分类
    if (node.isRoot && node.tp_id) {
      try {
        // 使用缓存版本的函数
        const children = await buildTreeFromResponseData(node.tp_id, 0, tpDataCacheRef.current);
        let cloneTreeData = _.cloneDeep(treeData);
        let cloneTreeNodeChildrenMap = _.cloneDeep(treeNodeChildrenMap);

        const target = findNodeByKey(cloneTreeData, node.key);
        if (target) {
          target.children = children;
          cloneTreeNodeChildrenMap[node.key] = children;
          setTreeData(cloneTreeData);
          setTreeNodeChildrenMap(cloneTreeNodeChildrenMap);
        }
      } catch (err) {
      }

      return new Promise<void>((resolve) => {
        resolve()
      })
    }

    // 如果是classify类型节点，懒加载其子节点
    if (nodeType === "classify" || node.nodeType === "classify") {
      let cloneTreeData = _.cloneDeep(treeData);
      let cloneTreeNodeChildrenMap = _.cloneDeep(treeNodeChildrenMap);
      
      try {
        // 优化：如果缓存中已有该tp_id的数据，直接从缓存构建，无需API调用
        let children: any[];
        if (tpDataCacheRef.current.has(node.tp_id)) {
          const cachedData = tpDataCacheRef.current.get(node.tp_id)!;
          children = buildTreeFromCachedData(cachedData, node.tp_id, node.id);
        } else {
          // 第一次加载该tp_id，需要调用API并缓存
          children = await buildTreeFromResponseData(node.tp_id, node.id, tpDataCacheRef.current);
        }
        
        const target = findNodeByKey(cloneTreeData, node.key);
        if (target) {
          target.children = children;
          cloneTreeNodeChildrenMap[node.key] = children;
          setTreeData(cloneTreeData);
          setTreeNodeChildrenMap(cloneTreeNodeChildrenMap);
        }
        
        return new Promise<void>((resolve) => {
          resolve()
        })
      } catch (err) {
        return new Promise<void>((resolve) => {
          resolve()
        })
      }
    }

    // 其他类型节点的处理逻辑可以在这里添加

    return new Promise<void>((resolve) => {
      resolve()
    })
  }, [treeData, treeNodeChildrenMap]);

  // 节点选择处理
  const handleNodeSelect = React.useCallback(async (_key: any, node: any) => {
    setSelectedKeys([node?.key]);
    const cloneNode = _.cloneDeep(node);

    // 获取选中节点下的所有子孙标签数据
    let allDescendants: any[] = [];

    try {
      if (node.isRoot && node.tp_id) {
        // 第一层：模板节点，获取该模板下的所有分类数据
        allDescendants = await getAllDescendants(0, node.tp_id, descendantsCache);
      } else if (node.nodeType === 'classify' && node.id && node.tp_id) {
        // 第二层及以下：分类节点，获取该分类下的所有子分类数据
        allDescendants = await getAllDescendants(node.id, node.tp_id, descendantsCache);
      }
    } catch {}

    // 将完整的子树数据添加到选中节点信息中
    cloneNode.allDescendants = allDescendants;
    dispatch(setSelectedTemplateNodeInfo(cloneNode as any));

    // 更新缓存
    setDescendantsCache(new Map(descendantsCache));
  }, [dispatch, descendantsCache]);

  // 清空缓存函数 - 使用函数式更新避免依赖循环
  const clearTpDataCache = React.useCallback((tp_id?: number) => {
    setTpDataCache(prevCache => {
      const newCache = new Map(prevCache);
      if (tp_id !== undefined) {
        // 清空指定 tp_id 的缓存
        newCache.delete(tp_id);
      } else {
        // 清空所有缓存
        newCache.clear();
      }
      return newCache;
    });
  }, []); // 移除 tpDataCache 依赖

  return {
    // 状态
    treeData,
    expandedKeys,
    selectedKeys,
    treeNodeChildrenMap,
    descendantsCache,
    rootLoading,
    
    // 操作函数
    setExpandedKeys,
    setSelectedKeys,
    onLoadData,
    refreshTree,
    clearTpDataCache,
    handleNodeSelect
  };
};